/******************************************
	Core Terraform and Project Related Variables
 *****************************************/
region         = "us-central1"
project_id     = "prod-web-api-7f60bf"
project_number = 71556946473
environment    = "prod"

#database settings
database_version         = "POSTGRES_13"
zone                     = "us-central1-c"
tier                     = "db-custom-16-61440"
replica_tier             = "db-custom-4-16384"
replica_active           = true
activation_policy        = "ALWAYS"
availability_type        = "REGIONAL"
secretname_user_name     = "prod-pgadmin-username"
secretname_user_password = "prod-pgadmin-password"

backup_configuration = {
  enabled                        = true
  start_time                     = "00:00"
  location                       = "us"
  point_in_time_recovery_enabled = true
}

pg_superuser_username_secret = "prod-api-user-name"
pg_superuser_password_secret = "prod-api-user-password"

additional_users = [
  {
    name     = "prod-api-user-name"
    password = "prod-api-user-password"
  }
]

labels = {
  "vanta-owner"              = "cameron"
  "vanta-non-prod"           = "false"
  "vanta-description"        = "prod-api-server-resources"
  "vanta-contains-user-data" = "true"
  "vanta-user-data-stored"   = "user-email-addresses-and-preferences"
  "vanta-contains-ephi"      = "true"
}

#sub network used by all the vms
subnetwork_project = "prod-network-bfb30f"
subnetwork         = "prod-central1-compute"

dev_group = "<EMAIL>"

#cloud run
serverless_vpc_connector_id = "projects/prod-network-bfb30f/locations/us-central1/connectors/svpc-prod-us-central1"

# Database
database_monitoring_enabled          = true
database_monitoriing_tags            = ["env:prod", "project-id:prod-web-api-7f60bf", "team:platform-services"]
database_monitoring_warning_channel  = "@slack-alert-api-server-prod"
database_monitoring_critical_channel = "@webhook-incident_io"

# Exit Node
exit_node_instance_type = "n2d-standard-4"
exit_node_sub_instances = [{
  name : "b",
  create_exit_node_instance : true
  }, {
  name : "c",
  create_exit_node_instance : true
  }, {
  name : "d",
  create_exit_node_instance : true
  }, {
  name : "e",
  create_exit_node_instance : true
  }, {
  name : "f",
  create_exit_node_instance : true
  }, {
  name : "g",
  create_exit_node_instance : false
  }, {
  name : "h",
  create_exit_node_instance : false
  }, {
  name : "i",
  create_exit_node_instance : false
  }, {
  name : "j",
  create_exit_node_instance : false
  }, {
  name : "k",
  create_exit_node_instance : false
}]
exit_node_tags = ["exit-node", "tailscale", "edge"]

# KEDA
keda_sa = "serviceAccount:<EMAIL>"

# Site Information
sites = [
  {
    site_id = "APELLA-DEMO-CENTRAL",
  },
  {
    site_id = "APELLA-DEMO-EAST",
  },
  {
    site_id = "APELLA-DEMO-MOUNTAIN",
  },
  {
    site_id = "APELLA-DEMO-WEST",
  },
  {
    site_id = "BMH-MEMPH-GEN",
  },
  {
    site_id              = "HF-VH02",
    confidence_threshold = 0.65,
    time_threshold       = "PT01M",
    days_of_week         = "1-5",
    timezone             = "US/Eastern",
  },
  {
    site_id = "HMH-DUNN03",
  },
  {
    site_id = "HMH-DUNN06",
  },
  {
    site_id = "HMH-HMBA",
  },
  {
    site_id = "HMH-HMBT-ASC",
  },
  {
    site_id = "HMH-HMBT-LD",
  },
  {
    site_id = "HMH-HMBT-OR",
  },
  {
    site_id = "HMH-HMCL-ASC",
  },
  {
    site_id = "HMH-HMCL-CBC",
  },
  {
    site_id = "HMH-HMCL-OR",
  },
  {
    site_id = "HMH-HMCY-CATH",
  },
  {
    site_id = "HMH-HMCY-ENDO",
  },
  {
    site_id = "HMH-HMCY-IR",
  },
  {
    site_id = "HMH-HMCY-LD",
  },
  {
    site_id = "HMH-HMCY-OR",
  },
  {
    site_id = "HMH-HMSL-LD",
  },
  {
    site_id = "HMH-HMSL-OR",
  },
  {
    site_id = "HMH-HMTW",
  },
  {
    site_id = "HMH-HMWB-C",
  },
  {
    site_id = "HMH-HMWB-N",
  },
  {
    site_id = "HMH-HMWB-S",
  },
  {
    site_id = "HMH-HMW-LD",
  },
  {
    site_id = "HMH-HMW-OR",
  },
  {
    site_id = "HMH-LD06",
  },
  {
    site_id = "HMH-MAIN03",
  },
  {
    site_id = "HMH-OPC18",
  },
  {
    site_id = "HMH-OPC19",
  },
  {
    site_id = "HMH-WT03",
  },
  {
    site_id = "HMHN-HUMC-CAS",
  },
  {
    site_id = "HMHN-HUMC-HTP",
  },
  {
    site_id = "HMHN-JFK-MOR",
  },
  {
    site_id = "HMHN-JSUMC-OR",
  },
  {
    site_id = "lab_1",
  },
  {
    site_id = "LBHS-GOR",
  },
  {
    site_id = "LBHS-RIAO",
  },
  {
    site_id = "MAYO-MCJ01",
  },
  {
    site_id = "MAYO-MCJ04",
  },
  {
    site_id = "MUSC-MAIN4",
  },
  {
    site_id = "nb_medical_center_fairfield",
  },
  {
    site_id = "NYU-KP4",
  },
  {
    site_id = "NYU-KP5",
  },
  {
    site_id = "NYU-KP5-CATH",
  },
  {
    site_id = "NYU-LI4",
  },
  {
    site_id = "sacred_heart",
  },
  {
    site_id = "seattle_grace",
  },
  {
    site_id = "seattle_presbyterian",
  },
  {
    site_id = "TGH-CVTOR03",
  },
  {
    site_id = "TGH-MAIN02",
  },
]
